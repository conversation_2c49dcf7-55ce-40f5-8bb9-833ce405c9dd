<template>
  <div class="transit-map-page">
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">公交调度地图</h2>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>实时监控</el-breadcrumb-item>
          <el-breadcrumb-item>地图监控</el-breadcrumb-item>
          <el-breadcrumb-item>公交调度地图</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-right">
        <el-space>
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
        </el-space>
      </div>
    </div>

    <div class="fullscreen-map">
      <el-card shadow="hover">
        <div class="map-container">
          <el-amap
            :zoom="12"
            :center="[116.397428, 39.90923]"
            map-style="normal"
            class="transit-map"
            @complete="onMapComplete"
          >
            <el-amap-control-scale />
            <el-amap-control-tool-bar />
            <el-amap-marker
              :position="[116.397428, 39.90923]"
              title="天安门站"
            />
            <el-amap-marker
              :position="[116.407428, 39.90923]"
              title="王府井站"
            />
          </el-amap>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup name="TransitMapView" lang="ts">
import { ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.back()
}

const onMapComplete = (map: any) => {
  console.log('公交调度地图加载完成:', map)
}
</script>

<style scoped>
.transit-map-page {
  padding: 16px;
  height: 100vh;
  overflow: hidden;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.fullscreen-map {
  height: calc(100vh - 120px);
}

.map-container {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.transit-map {
  width: 100%;
  height: 100%;
}
</style>
