import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'

export interface ActionItem {
  key: string
  label: string
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  icon: string
  permissions: string[]
  handler?: () => void
}

export interface StatItem {
  key: string
  label: string
  value: string | number
  icon: string
  type: 'primary' | 'success' | 'warning' | 'danger'
}

// 页面操作按钮配置
const pageActionsConfig: Record<string, ActionItem[]> = {
  '/vehicle': [
    { 
      key: 'add', 
      label: '新增车辆', 
      type: 'primary', 
      icon: 'Plus', 
      permissions: ['vehicle:add'],
      handler: () => ElMessage.success('跳转到新增车辆页面')
    },
    { 
      key: 'import', 
      label: '导入数据', 
      type: 'success', 
      icon: 'Upload', 
      permissions: ['vehicle:import'],
      handler: () => ElMessage.info('打开导入对话框')
    },
    { 
      key: 'export', 
      label: '导出数据', 
      type: 'warning', 
      icon: 'Download', 
      permissions: ['vehicle:export'],
      handler: () => ElMessage.info('开始导出数据')
    },
    { 
      key: 'batch', 
      label: '批量操作', 
      type: 'info', 
      icon: 'Operation', 
      permissions: ['vehicle:batch'],
      handler: () => ElMessage.info('批量操作功能')
    }
  ],
  '/driver': [
    { 
      key: 'add', 
      label: '新增司机', 
      type: 'primary', 
      icon: 'Plus', 
      permissions: ['driver:add'],
      handler: () => ElMessage.success('跳转到新增司机页面')
    },
    { 
      key: 'schedule', 
      label: '排班管理', 
      type: 'info', 
      icon: 'Calendar', 
      permissions: ['driver:schedule'],
      handler: () => ElMessage.info('打开排班管理')
    },
    { 
      key: 'batch', 
      label: '批量操作', 
      type: 'warning', 
      icon: 'Operation', 
      permissions: ['driver:batch'],
      handler: () => ElMessage.info('司机批量操作')
    }
  ],
  '/route': [
    { 
      key: 'add', 
      label: '新增路线', 
      type: 'primary', 
      icon: 'Plus', 
      permissions: ['route:add'],
      handler: () => ElMessage.success('跳转到新增路线页面')
    },
    { 
      key: 'map', 
      label: '路线地图', 
      type: 'info', 
      icon: 'Location', 
      permissions: ['route:view'],
      handler: () => ElMessage.info('打开路线地图')
    },
    { 
      key: 'optimize', 
      label: '路线优化', 
      type: 'success', 
      icon: 'Setting', 
      permissions: ['route:optimize'],
      handler: () => ElMessage.info('启动路线优化')
    }
  ],
  '/dispatch': [
    { 
      key: 'create', 
      label: '创建调度', 
      type: 'primary', 
      icon: 'Plus', 
      permissions: ['dispatch:add'],
      handler: () => ElMessage.success('创建新调度计划')
    },
    { 
      key: 'auto', 
      label: '自动调度', 
      type: 'success', 
      icon: 'Magic', 
      permissions: ['dispatch:auto'],
      handler: () => ElMessage.info('启动自动调度')
    },
    { 
      key: 'monitor', 
      label: '实时监控', 
      type: 'warning', 
      icon: 'Monitor', 
      permissions: ['dispatch:monitor'],
      handler: () => ElMessage.info('打开实时监控')
    }
  ],
  '/report': [
    { 
      key: 'generate', 
      label: '生成报表', 
      type: 'primary', 
      icon: 'Document', 
      permissions: ['report:generate'],
      handler: () => ElMessage.success('生成报表')
    },
    { 
      key: 'export', 
      label: '导出报表', 
      type: 'success', 
      icon: 'Download', 
      permissions: ['report:export'],
      handler: () => ElMessage.info('导出报表数据')
    }
  ],
  '/system': [
    { 
      key: 'user', 
      label: '用户管理', 
      type: 'primary', 
      icon: 'User', 
      permissions: ['system:user'],
      handler: () => ElMessage.success('跳转到用户管理')
    },
    { 
      key: 'role', 
      label: '角色管理', 
      type: 'info', 
      icon: 'Avatar', 
      permissions: ['system:role'],
      handler: () => ElMessage.info('跳转到角色管理')
    },
    { 
      key: 'config', 
      label: '系统配置', 
      type: 'warning', 
      icon: 'Setting', 
      permissions: ['system:config'],
      handler: () => ElMessage.info('打开系统配置')
    }
  ]
}

// 页面统计数据配置（模拟数据）
const getStatsByRoute = (route: string): StatItem[] => {
  const statsMap: Record<string, StatItem[]> = {
    '/vehicle': [
      { key: 'total', label: '总车辆数', value: 156, icon: 'car', type: 'primary' },
      { key: 'online', label: '在线车辆', value: 128, icon: 'check', type: 'success' },
      { key: 'maintenance', label: '维护中', value: 12, icon: 'tool', type: 'warning' },
      { key: 'offline', label: '离线车辆', value: 16, icon: 'close', type: 'danger' }
    ],
    '/driver': [
      { key: 'total', label: '总司机数', value: 203, icon: 'user', type: 'primary' },
      { key: 'working', label: '在岗司机', value: 145, icon: 'check', type: 'success' },
      { key: 'rest', label: '休息中', value: 58, icon: 'time', type: 'warning' }
    ],
    '/route': [
      { key: 'total', label: '总路线数', value: 48, icon: 'route', type: 'primary' },
      { key: 'active', label: '运营中', value: 42, icon: 'check', type: 'success' },
      { key: 'suspended', label: '暂停运营', value: 6, icon: 'pause', type: 'warning' }
    ],
    '/dispatch': [
      { key: 'today', label: '今日调度', value: 89, icon: 'schedule', type: 'primary' },
      { key: 'completed', label: '已完成', value: 76, icon: 'check', type: 'success' },
      { key: 'pending', label: '进行中', value: 13, icon: 'loading', type: 'warning' }
    ]
  }
  
  const baseRoute = '/' + route.split('/')[1]
  return statsMap[baseRoute] || []
}

export function usePageActions() {
  const userStore = useUserStore()
  
  // 检查权限
  const hasPermission = (permissions: string[]): boolean => {
    if (!permissions || permissions.length === 0) return true
    
    const userPermissions = userStore.permissions
    return userPermissions.some(perm => 
      permissions.includes(perm) || perm === '*:*:*'
    )
  }
  
  // 根据当前路由和权限获取操作按钮
  const getPageActions = async (pagePath: string): Promise<ActionItem[]> => {
    const baseRoute = '/' + pagePath.split('/')[1]
    const actions = pageActionsConfig[baseRoute] || []
    
    // 根据权限过滤操作按钮
    return actions.filter(action => hasPermission(action.permissions))
  }
  
  // 获取页面统计数据
  const getPageStats = async (pagePath: string): Promise<StatItem[]> => {
    // 模拟异步获取数据
    await new Promise(resolve => setTimeout(resolve, 100))
    
    return getStatsByRoute(pagePath)
  }
  
  // 执行操作
  const executeAction = (action: ActionItem) => {
    if (!hasPermission(action.permissions)) {
      ElMessage.error('权限不足')
      return
    }
    
    if (action.handler) {
      action.handler()
    } else {
      ElMessage.info(`执行操作: ${action.label}`)
    }
  }
  
  return {
    getPageActions,
    getPageStats,
    executeAction,
    hasPermission
  }
}

// 菜单权限控制
export function useMenuPermission() {
  const userStore = useUserStore()
  
  // 检查菜单权限
  const hasMenuPermission = (permissions: string[]): boolean => {
    if (!permissions || permissions.length === 0) return true
    
    const userPermissions = userStore.permissions
    const userRoles = userStore.roles
    
    // 超级管理员权限
    if (userRoles.includes('admin') || userRoles.includes('superadmin')) {
      return true
    }
    
    // 检查具体权限
    return userPermissions.some(perm => 
      permissions.includes(perm) || perm === '*:*:*'
    )
  }
  
  // 根据权限过滤菜单
  const filterMenusByPermission = <T extends { permissions?: string[] }>(menus: T[]): T[] => {
    return menus.filter(menu => {
      if (!menu.permissions) return true
      return hasMenuPermission(menu.permissions)
    })
  }
  
  return {
    hasMenuPermission,
    filterMenusByPermission
  }
}